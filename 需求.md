# 专注驾驶应用需求文档

## 📱 应用概述
这是一个基于Godot 4.5开发的手机专注计时应用，帮助用户管理和跟踪专注会话。

## 🎯 核心功能需求

### 1. 用户界面激活
- **需求**：点击屏幕的任何地方都可以激活显示UI
- **澄清问题**：具体要显示哪个UI界面？
  - [ ] 主界面（项目选择）
  - [ ] 计时器界面
  - [ ] 设置界面
- **建议**：明确"UI"指代的具体界面

### 2. 数据存储
- **数据源**：本地SQLite文件
- **参考库**：https://github.com/2shady4u/godot-sqlite
- **数据结构**：
  - **项目表** (`projects`)
    - `id`：主键，自增
    - `name`：项目名称，唯一，最大25字符
    - `total_focus_time`：总专注时长（秒）
  - **专注会话表** (`focus_sessions`)
    - `id`：主键，自增
    - `project_id`：关联项目ID
    - `note`：备注
    - `start_time`：开始时间戳
    - `duration`：会话时长（秒）
    - `created_at`：创建时间戳

### 3. 默认项目管理
- **规则**：没有项目时自动新建名称为"默认"的项目
- **澄清问题**：删除所有项目后是否需要重新创建"默认"项目？
- **建议**：确保始终至少存在一个项目

### 4. 数据持久化
- **时机**：每次专注完成时将结果持久化到本地SQLite
- **重启行为**：重开后不需要恢复计时
- **暂停行为**：暂停状态计时器停止计时
- **澄清问题**：暂停状态下关闭应用后重开的具体行为？
  - [ ] 清除暂停状态，回到初始状态
  - [ ] 保持暂停状态但不恢复计时
  - [ ] 其他行为

### 5. 项目管理
- **删除功能**：支持删除当前项目
- **约束**：项目名称不允许重名，最大25字符
- **澄清问题**：删除最后一个项目后的处理方式？

### 6. 界面布局
- **排列方式**：水平排列
- **样式**：先不考虑具体样式和动画
- **澄清问题**：什么内容需要水平排列？
  - [ ] 项目列表
  - [ ] 功能按钮
  - [ ] 计时器显示
- **建议**：明确布局对象

### 7. UI显示控制
- **显示方式**：当前场景内通过Panel显示对应UI
- **关闭方式**：点击Panel左上角的X按钮关闭
- **自动暂停**：进入其他Panel时自动暂停当前计时

### 8. 功能支持范围
- **澄清问题**："都需要支持"具体指什么功能？
- **动画效果**：先随意实现，不需要特别处理快速滑动

## 🔄 循环与显示

### 4.1 循环支持
- **需求**：支持无限循环
- **排列**：按照当前文本的顺序排列
- **澄清问题**：什么内容需要无限循环？
  - [ ] 项目列表轮播
  - [ ] 计时器状态循环
  - [ ] 其他内容

### 时间显示
- **最小单位**：秒
- **当前状态**：先不显示
- **澄清问题**：什么内容以秒为单位但暂不显示？

### 字符限制
- **项目名称**：最大25字符，不允许重名

## ❓ 需要澄清的问题

### 高优先级澄清
1. **UI激活**：点击屏幕激活的具体是哪个界面？
2. **水平排列**：哪些内容需要水平排列？
3. **无限循环**：什么内容需要支持无限循环？
4. **功能支持**："都需要支持"的具体功能范围？

### 中优先级澄清
1. **项目删除**：删除所有项目后是否重新创建默认项目？
2. **暂停重启**：暂停状态下关闭应用重开的行为？
3. **时间显示**：秒级时间单位指什么内容？

## 📋 建议的开发方案

### 阶段一：核心功能
1. 完善数据库管理（已基本完成）
2. 实现基础计时器功能
3. 创建简单的UI界面

### 阶段二：交互优化
1. 实现屏幕点击激活
2. 添加项目管理功能
3. 完善UI显示控制

### 阶段三：功能扩展
1. 根据澄清后的需求实现循环功能
2. 优化用户体验
3. 添加数据统计功能

## 🚀 下一步行动

请针对上述澄清问题提供明确答案，以便制定详细的开发计划和实现方案。特别需要明确：

1. UI激活的具体目标界面
2. 水平排列的内容范围
3. 无限循环的应用场景
