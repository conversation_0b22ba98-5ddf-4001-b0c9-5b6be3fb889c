[gd_scene load_steps=4 format=3 uid="uid://c2x8iak4j5l6m"]

[ext_resource type="Script" uid="uid://xlawbburrqo3" path="res://src/driving/ui/components/focus_timer.gd" id="1_timer"]
[ext_resource type="FontFile" uid="uid://bcpy6cc2d6noy" path="res://resource/fonts/editundo.ttf" id="2_hfkrg"]

[sub_resource type="LabelSettings" id="LabelSettings_ffiig"]
font = ExtResource("2_hfkrg")
font_size = 128
font_color = Color(0.94117653, 0.7921569, 0.04313725, 1)
outline_size = 3
outline_color = Color(0, 0, 0, 1)
shadow_size = 10
shadow_color = Color(0.5921569, 0.007843138, 0.7294118, 1)
shadow_offset = Vector2(5, 8)

[node name="FocusTimer" type="Control"]
layout_mode = 3
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -25.0
offset_right = 100.0
offset_bottom = 25.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_timer")

[node name="TimeLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
text = "00:00:00"
label_settings = SubResource("LabelSettings_ffiig")
horizontal_alignment = 1
vertical_alignment = 1
