class_name ProjectSelector
extends Control

## 项目选择器组件
## 实现横向Tabs样式的项目切换器，支持拖拽切换和环形循环

signal project_selected(project: ProjectData)

@onready var scroll_container: ScrollContainer = $VBoxContainer/ScrollContainer
@onready var projects_container: HBoxContainer = $VBoxContainer/ScrollContainer/ProjectsContainer

var projects: Array[ProjectData] = []
var current_index: int = 0
var project_buttons: Array[Button] = []

const PROJECT_BUTTON_WIDTH = 120
const PROJECT_BUTTON_HEIGHT = 60

func _ready() -> void:
	# 设置滚动容器 - 隐藏滚动条但保持滚动功能
	scroll_container.horizontal_scroll_mode = ScrollContainer.SCROLL_MODE_DISABLED
	scroll_container.vertical_scroll_mode = ScrollContainer.SCROLL_MODE_DISABLED

## 设置项目列表
## [br]更新显示的项目列表并重新构建UI
func set_projects(project_list: Array[ProjectData]) -> void:
	projects = project_list
	_rebuild_project_buttons()
	
	# 确保选中索引有效
	if projects.size() > 0:
		current_index = clamp(current_index, 0, projects.size() - 1)
		_update_selection()
	else:
		current_index = 0

## 获取当前选中的项目
func get_current_project() -> ProjectData:
	if current_index >= 0 and current_index < projects.size():
		return projects[current_index]
	return null

## 设置当前选中的项目索引
func set_current_index(index: int) -> void:
	if index >= 0 and index < projects.size():
		current_index = index
		_update_selection()

## 重建项目按钮
func _rebuild_project_buttons() -> void:
	# 清除现有按钮
	for button in project_buttons:
		button.queue_free()
	project_buttons.clear()
	
	# 创建新按钮
	for i in range(projects.size()):
		var project = projects[i]
		var button = _create_project_button(project, i)
		projects_container.add_child(button)
		project_buttons.append(button)

## 创建项目按钮
func _create_project_button(project: ProjectData, index: int) -> Button:
	var button = Button.new()
	button.text = project.name
	button.custom_minimum_size = Vector2(PROJECT_BUTTON_WIDTH, PROJECT_BUTTON_HEIGHT)
	button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	
	# 连接点击信号
	button.pressed.connect(_on_project_button_pressed.bind(index))
	
	return button

## 更新选中状态
func _update_selection() -> void:
	# 更新按钮样式
	for i in range(project_buttons.size()):
		var button = project_buttons[i]
		if i == current_index:
			# 选中状态：放大并居中
			button.add_theme_stylebox_override("normal", _get_selected_style())
			button.scale = Vector2(1.2, 1.2)
		else:
			# 普通状态
			button.remove_theme_stylebox_override("normal")
			button.scale = Vector2(1.0, 1.0)
	
	# 滚动到选中项目
	_scroll_to_current()
	
	# 发送选中信号
	if current_index >= 0 and current_index < projects.size():
		project_selected.emit(projects[current_index])

## 获取选中状态的样式
func _get_selected_style() -> StyleBox:
	var style = StyleBoxFlat.new()
	style.bg_color = Color.BLUE
	style.border_width_left = 2
	style.border_width_right = 2
	style.border_width_top = 2
	style.border_width_bottom = 2
	style.border_color = Color.WHITE
	return style

## 滚动到当前选中项目
func _scroll_to_current() -> void:
	if project_buttons.size() == 0 or current_index < 0:
		return
	
	var button = project_buttons[current_index]
	var button_center = button.position.x + button.size.x / 2
	var container_center = scroll_container.size.x / 2
	var target_scroll = button_center - container_center
	
	# 平滑滚动
	var tween = create_tween()
	tween.tween_property(scroll_container, "scroll_horizontal", int(target_scroll), 0.3)

## 切换到下一个项目（支持循环）
func next_project() -> void:
	if projects.size() > 0:
		current_index = (current_index + 1) % projects.size()
		_update_selection()

## 切换到上一个项目（支持循环）
func previous_project() -> void:
	if projects.size() > 0:
		current_index = (current_index - 1 + projects.size()) % projects.size()
		_update_selection()

## 处理拖拽输入
func _gui_input(event: InputEvent) -> void:
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_LEFT:
			if event.pressed:
				_start_drag(event.position)
			else:
				_end_drag(event.position)
	elif event is InputEventMouseMotion and _is_dragging:
		_update_drag(event.position)

var _is_dragging: bool = false
var _drag_start_pos: Vector2
var _drag_start_scroll: int

## 开始拖拽
func _start_drag(pos: Vector2) -> void:
	_is_dragging = true
	_drag_start_pos = pos
	_drag_start_scroll = scroll_container.scroll_horizontal

## 更新拖拽
func _update_drag(pos: Vector2) -> void:
	if not _is_dragging:
		return
	
	var delta = _drag_start_pos.x - pos.x
	scroll_container.scroll_horizontal = _drag_start_scroll + int(delta)

## 结束拖拽
func _end_drag(pos: Vector2) -> void:
	if not _is_dragging:
		return
	
	_is_dragging = false
	
	# 检查是否需要切换项目
	var drag_distance = abs(_drag_start_pos.x - pos.x)
	if drag_distance > 50:  # 拖拽阈值
		if pos.x < _drag_start_pos.x:
			next_project()
		else:
			previous_project()
	else:
		# 回弹到当前位置
		_scroll_to_current()

## 项目按钮点击事件
func _on_project_button_pressed(index: int) -> void:
	current_index = index
	_update_selection()
