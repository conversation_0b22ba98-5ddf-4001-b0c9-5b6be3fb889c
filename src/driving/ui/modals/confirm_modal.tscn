[gd_scene load_steps=2 format=3 uid="uid://b8qy3x1vn8c8r"]

[ext_resource type="Script" path="res://src/driving/ui/modals/confirm_modal.gd" id="1_confirm"]

[node name="ConfirmModal" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_confirm")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0, 0, 0, 0.5)

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="Panel" type="Panel" parent="CenterContainer"]
layout_mode = 2
custom_minimum_size = Vector2(300, 200)

[node name="VBoxContainer" type="VBoxContainer" parent="CenterContainer/Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0

[node name="TitleLabel" type="Label" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
text = "确认"
horizontal_alignment = 1

[node name="MessageLabel" type="Label" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
text = "确认执行此操作？"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2

[node name="ButtonContainer" type="HBoxContainer" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
alignment = 1

[node name="CancelButton" type="Button" parent="CenterContainer/Panel/VBoxContainer/ButtonContainer"]
layout_mode = 2
text = "取消"

[node name="ConfirmButton" type="Button" parent="CenterContainer/Panel/VBoxContainer/ButtonContainer"]
layout_mode = 2
text = "确认"
