# 专注软件主界面UI需求文档

为我实现这个专注软件主界面的ui。ui分为3个部分，2个状态。要求ui逻辑和业务邏輯分离，避免过度耦合。

## 📱 基础交互逻辑

- **平台**：手机应用
- **UI激活**：点击屏幕任何地方都可以激活显示UI
- **UI显示**：默认隐藏，点击后通过滑入+淡入动画展示UI
- **UI隐藏**：5秒不操作后通过滑出+淡出动画隐藏UI

## 🗄️ 数据结构设计

### 数据存储
- **数据源**：本地SQLite文件
- **参考实现**：https://github.com/2shady4u/godot-sqlite

### 数据库表结构

**projects 表（项目表）**：
- id (INTEGER PRIMARY KEY)
- name (TEXT) - 项目名称，最大25字符，不允许重名
- total_focus_time (INTEGER) - 总专注时长，单位：秒

**focus_sessions 表（专注会话表）**：
- id (INTEGER PRIMARY KEY)
- project_id (INTEGER) - 关联项目ID
- note (TEXT) - 备注
- start_time (INTEGER) - 开始时间戳
- duration (INTEGER) - 会话时长，单位：秒
- created_at (INTEGER) - 创建时间戳

### 业务逻辑
- **计时单位**：最小单位为秒
- **数据持久化**：每次专注完成时将结果保存到SQLite
- **应用重启**：重开后不需要恢复计时状态
- **默认项目**：没有项目时自动新建名称为"默认"的项目

## 🎨 UI布局结构

### 顶部区
- **位置**：水平居中，垂直距顶部10%
- **尺寸**：宽约40%，高20%

#### 初始状态
不显示任何组件

#### 专注状态
显示本次专注的时长，格式为HH:MM:SS

### 设置区
- **位置**：右上角的按钮组
- **状态**：在任何状态都是相同的逻辑
- **页面切换**：通过Panel在当前场景内显示对应UI
- **返回方式**：点击Panel左上角的x按钮关闭
- **专注状态处理**：进入其他Panel时自动暂停计时

#### 按钮列表
1. **统计按钮**：图标为文件，点击后加载统计页面
2. **商城按钮**：图标为商店，点击后加载商城页面  
3. **设置按钮**：图标为齿轮，点击后加载设置页面

### 底部区
- **位置**：水平居中，垂直距底部10%
- **尺寸**：宽约40%，高20%

#### 初始状态

**项目选择器**：
- **样式**：类似于横向的Tabs
- **显示逻辑**：激活项目居中放大显示
- **切换方式**：支持滑动拖拽切换到其他项目（支持鼠标拖拽和触屏）
- **显示数量**：默认最多同时显示5个项目
- **循环逻辑**：支持无限循环切换
- **排列顺序**：按照当前文本的顺序排列

**功能按钮**：
- **加号按钮**：位于项目选择器右边
  - 点击后弹出文本输入modal
  - 确认后可以新建项目
  - 项目名称限制：最大25字符，不允许重名
- **垃圾桶按钮**：位于项目选择器左边
  - 点击后弹出modal确认是否删除当前项目
  - 确认后删除项目

**开始按钮**：
- **位置**：项目选择器下方居中
- **文案**："开始驾驶"
- **功能**：点击后进入专注状态

#### 专注状态

**按钮组**：水平排列的4个按钮
1. **取消按钮（x）**：
   - 点击后弹出modal询问是否取消
   - 确认后退出专注状态进入初始状态

2. **暂停按钮（⏸️）**：
   - 点击后暂停专注状态的计时
   - 在暂停时隐藏此按钮

3. **继续按钮**：
   - 点击后继续专注状态的计时
   - 在计时期间隐藏此按钮

4. **完成按钮**：
   - 点击后弹出modal询问是否完成
   - 确认后退出专注状态进入初始状态
   - 完成时将会话数据保存到数据库

## 🎯 技术要求

### 架构设计
- **UI逻辑与业务逻辑分离**：避免过度耦合
- **数据层**：负责SQLite数据库操作
- **业务逻辑层**：负责专注会话管理和计时逻辑
- **UI层**：负责界面显示和用户交互

### 交互细节
- **Modal样式**：使用最简单的效果
- **动画效果**：先不考虑复杂动画，随意实现即可
- **快速滑动**：不需要特别处理
- **暂停状态**：计时器停止计时即可
